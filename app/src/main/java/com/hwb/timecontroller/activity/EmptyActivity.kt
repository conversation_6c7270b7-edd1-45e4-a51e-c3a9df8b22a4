package com.hwb.timecontroller.activity

import android.app.ActivityManager
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.UserManager
import android.provider.Settings
import android.view.WindowManager
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.hi.dhl.binding.viewbind
import com.hwb.timecontroller.AppDeviceAdminReceiver
import com.hwb.timecontroller.R
import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.business.AppLifecycleManager
import com.hwb.timecontroller.business.WhitelistManager
import com.hwb.timecontroller.constant.Constant
import com.hwb.timecontroller.databinding.ActivityEmptyBinding
import com.hwb.timecontroller.service.AppLifecycleManagerService
import com.hwb.timecontroller.service.FloatingWindowService

import com.hwb.timecontroller.utils.AppUsageHelper
import com.hwb.timecontroller.utils.dp2px
import com.kongzue.dialogx.dialogs.MessageDialog
import com.elvishew.xlog.XLog
import com.hwb.timecontroller.MyApplication

/**
 * 应用启动页面
 * 显示简洁的加载界面，延迟后跳转到主Activity
 */
class EmptyActivity : AppCompatActivity() {
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName

    private var initialize = false
    private val binding: ActivityEmptyBinding by viewbind()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_empty)

        // 设置版本信息
        setupVersionInfo()

        devicePolicyManager = getSystemService(DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, AppDeviceAdminReceiver::class.java)

        val isDeviceOwner = devicePolicyManager.isDeviceOwnerApp(packageName)
        if (!isDeviceOwner) {
            MessageDialog.build().setCancelable(false).setOkButton("确定")
                .setMaxWidth(500.dp2px)
                .setTitle("警告")
                .setMessage("当前应用不是设备所有者，无法使用控制器").setOkButton { dialog, v ->
                    finish()
                    false
                }.show()
            return
        }

        setupLockTaskPackages()
        // 移除startLockTask()调用，改为在homePackage启动时进入LockTask
        initialize = true

        // 同步管控状态
        AdminManager.syncGovernanceState(this)

        if (checkUsageAccessPermission() && checkOverlayPermission()) {
            initService()
        }

        //让屏幕不休眠
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        //不显示错误对话框
        devicePolicyManager.addUserRestriction(
            adminComponent,
            UserManager.DISALLOW_SYSTEM_ERROR_DIALOGS
        )

        // 异步更新远程白名单（放在onCreate最后，确保Device Owner权限已验证）
        WhitelistManager.fetchAndUpdateRemoteWhitelist(this)
    }

    //启动各个service
    private fun initService() {
        FloatingWindowService.start(this)
        AppLifecycleManagerService.start(this)
    }


    private fun setupLockTaskPackages() {
        try {
            // 使用 WhitelistManager 初始化白名单，如果有设备拥有者权限会自动设置 LockTask 白名单
            WhitelistManager.initializeWhitelist(this)
        } catch (e: Exception) {
            // 即使LockTask设置失败，也不影响核心功能
            XLog.w("LockTask白名单设置失败", e)
        }
    }

    /**
     * 设置版本信息显示
     */
    private fun setupVersionInfo() {
        try {
            val versionName = packageManager.getPackageInfo(packageName, 0).versionName
            findViewById<TextView>(R.id.tv_version).text = "v$versionName"
        } catch (e: PackageManager.NameNotFoundException) {
            XLog.w("获取版本信息失败", e)
            // 如果获取失败，使用默认版本号
            findViewById<TextView>(R.id.tv_version).text = "v1.0.0"
        }
    }

    /**
     * 禁用返回键，防止用户在启动页按返回键退出应用
     */
    override fun onBackPressed() {
        // 在启动页不响应返回键
        // 如果需要允许用户退出，可以调用 super.onBackPressed()
        XLog.d("启动页忽略返回键操作")
    }

    override fun onDestroy() {
        // 移除stopLockTask()调用，因为EmptyActivity不再进入LockTask
        super.onDestroy()
        XLog.d("EmptyActivity onDestroy")

        // 如果被意外销毁，重新启动
        if (!isFinishing) {
            val restartIntent = Intent(this, EmptyActivity::class.java)
            restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(restartIntent)
        }
    }

    override fun onResume() {
        super.onResume()
        //初始化后，做一下分发
        if (initialize) {
            startNextActivity()
        }
    }

    private fun startNextActivity() {
        try {
            XLog.d("EmptyActivity执行初始应用跳转")

            // 检查权限状态
            val hasUsageAccessPermission = checkUsageAccessPermission()
            val hasOverlayPermission = checkOverlayPermission()
            val hasAllPermissions = hasUsageAccessPermission && hasOverlayPermission

            if (!hasAllPermissions) {
                XLog.d("权限不完整，启动MainActivity进行权限引导")
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                return
            }

            // 权限足够，检查当前实际状态
            val currentForegroundApp = AppLifecycleManager.getCurrentForegroundApp()
            val isHomePackageInForeground = currentForegroundApp == Constant.homePackage

            // 检查系统是否在LockTask模式
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val isInLockTaskMode =
                activityManager.lockTaskModeState == ActivityManager.LOCK_TASK_MODE_LOCKED

            if (isHomePackageInForeground && isInLockTaskMode) {
                XLog.d("homePackage已在前台且在LockTask模式，保持EmptyActivity在后台")
                // homePackage已在前台且在LockTask模式，EmptyActivity保持后台，不做任何操作
                moveTaskToBack(true)
            } else {
                if (isHomePackageInForeground) {
                    XLog.d("homePackage在前台但不在LockTask模式，重新以LockTask模式启动")
                } else {
                    XLog.d("权限完整，默认进入管控状态，启动homePackage")
                }
                // 启动homePackage进入LockTask管控模式
                WhitelistManager.openHomeApp()

                // 异步更新远程白名单
                WhitelistManager.fetchAndUpdateRemoteWhitelist(this)
            }
        } catch (e: Exception) {
            XLog.e("EmptyActivity执行初始应用跳转失败", e)
        }
    }



    //检查使用情况访问权限
    private fun checkUsageAccessPermission(): Boolean {
        return try {
            AppUsageHelper.hasUsageAccessPermission(this)
        } catch (e: Exception) {
            XLog.e("检查使用情况访问权限失败", e)
            false
        }
    }

    //检查悬浮窗权限
    private fun checkOverlayPermission(): Boolean {
        return Settings.canDrawOverlays(this)
    }
}
