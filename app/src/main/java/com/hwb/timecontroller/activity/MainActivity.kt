package com.hwb.timecontroller.activity

import android.app.admin.DevicePolicyManager
import android.content.ActivityNotFoundException
import android.content.ComponentName
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.InputType
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import com.elvishew.xlog.XLog
import com.hi.dhl.binding.viewbind
import com.hjq.toast.Toaster
import com.hwb.timecontroller.AppDeviceAdminReceiver
import com.hwb.timecontroller.BuildConfig
import com.hwb.timecontroller.MyApplication
import com.hwb.timecontroller.R
import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.business.UpdateManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.business.WhitelistManager
import com.hwb.timecontroller.constant.Constant
import com.hwb.timecontroller.databinding.ActivityMainBinding
import com.hwb.timecontroller.service.CountdownService
import com.hwb.timecontroller.utils.AppUsageHelper

import com.kongzue.dialogx.dialogs.InputDialog
import com.kongzue.dialogx.dialogs.MessageDialog
import com.kongzue.dialogx.util.InputInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch


class MainActivity : AppCompatActivity() {

    private val binding by viewbind<ActivityMainBinding>()

    // 协程作用域
    private val activityScope = CoroutineScope(Dispatchers.Main + Job())
    private var statusUpdateJob: Job? = null

    // 隐藏操作相关变量
    private val clickTimes = mutableListOf<Long>()
    private val clickTimeWindow = 3000L // 3秒时间窗口
    private val requiredClicks = 5 // 需要5次点击



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_main)

        initViews()

        setupClickListeners()

        // 启动时静默检查更新
        checkUpdateOnStartup()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        initViews()
    }

    private fun initViews() {
        //尝试初始化日志系统
        MyApplication.myApp.initLog()

        binding.btnStartGovernanceState.text =
            if (AdminManager.isGovernanceState) "退出管控状态" else "进入管控状态"
        binding.btnFinish.visibility =
            if (AdminManager.isGovernanceState) View.VISIBLE else View.GONE

        binding.tvVersion.text = BuildConfig.VERSION_NAME

        binding.llXiaomi.visibility = if (isXiaomi()) View.VISIBLE else View.GONE
    }

    private fun setupClickListeners() {
        binding.btnStartCountdownTest.setOnClickListener {
            startCountdownTest()
        }

        binding.btnWhitelistManager.setOnClickListener {
            openWhitelistManager()
        }

        binding.btnStartGovernanceState.setOnClickListener {
            if (AdminManager.isGovernanceState) {
                exitGovernanceState()
            } else {
                handleGovernanceStateClick()
            }
        }
        binding.btnFinish.setOnClickListener {
            finish()
//            moveTaskToBack(true)  // 只是把当前Task移到后台
        }

        binding.btnResetDeviceId.setOnClickListener {
            showDeviceIdResetPasswordDialog()
        }

        binding.btnCheckUpdate.setOnClickListener {
            checkForUpdates()
        }

        // 隐藏操作：tvTitle连续点击检测
        binding.tvTitle.setOnClickListener {
            handleTitleClick()
        }
        binding.btnXiaomiPopupPermission.setOnClickListener {
            openMiuiPopupPermissionSettings()
        }
        binding.btnXiaomiAutoStartPermission.setOnClickListener {
            openMiuiAutoStartSettings()
        }
        binding.btnXiaomiIgnorePower.setOnClickListener {
            openMiuiIgnorePowerSettings()
        }
    }

    //退出管控模式
    private fun exitGovernanceState() {
        try {
            AppDeviceAdminReceiver.isManualExit = true // 标记手动退出
            val devicePolicyManager = getSystemService(DEVICE_POLICY_SERVICE) as DevicePolicyManager
            val adminComponent = ComponentName(this, AppDeviceAdminReceiver::class.java)

            // 清空LockTask白名单
            devicePolicyManager.setLockTaskPackages(adminComponent, arrayOf())

            // MainActivity本身不在LockTask中，不需要调用stopLockTask()
            // 如果homePackage在LockTask中，需要通过其他方式退出

            // 更新管控状态
            AdminManager.changeGovernanceState(false)

            // 刷新UI
            initViews()

            XLog.d("已退出管控状态")
        } catch (e: Exception) {
            XLog.e("退出管控状态失败", e)
        }
    }

    private fun startCountdownTest() {
        InputDialog("设置", "", "确定", "取消", "")
            .setInputHintText("请输入管理员密码")
            .setInputInfo(InputInfo().apply {
                inputType = InputType.TYPE_CLASS_NUMBER    // 只允许输入数字
            })
            .setOkButton { baseDialog, v, inputStr ->
                startCountdown(inputStr)
                false
            }.show()
    }

    private fun startCountdown(minutesText: String) {

        if (minutesText.isEmpty()) {
            Toast.makeText(this, getString(R.string.error_enter_minutes), Toast.LENGTH_SHORT).show()
            return
        }

        val minutes = minutesText.toIntOrNull()
        if (minutes == null || minutes <= 0) {
            Toast.makeText(this, getString(R.string.error_invalid_minutes), Toast.LENGTH_SHORT)
                .show()
            return
        }

        val durationMillis = minutes * 60 * 1000L

        // 启动倒计时服务
        val intent = Intent(this, CountdownService::class.java)
        intent.putExtra("duration", durationMillis)
        startForegroundService(intent)

        // 更新UI状态
        binding.tvStatus.text = getString(R.string.status_countdown_started, minutes)

        Toast.makeText(this, getString(R.string.countdown_started), Toast.LENGTH_SHORT).show()

        moveTaskToBack(true)
    }



    private fun openWhitelistManager() {
        val intent = Intent(this, WhitelistActivity::class.java)
        startActivity(intent)
    }




    override fun onResume() {
        super.onResume()
    }




    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        // 悬浮窗权限现在由无障碍服务自动处理，不需要在这里处理
        XLog.d("onActivityResult: 悬浮窗权限由无障碍服务处理")
    }

    /**
     * 处理进入管控状态按钮点击事件
     */
    private fun handleGovernanceStateClick() {
        try {
            XLog.d("开始处理进入管控状态请求")

            // 按顺序检查权限：先检查使用情况访问权限，再检查悬浮窗权限
            if (!checkUsageAccessPermission()) {
                XLog.w("缺少使用情况访问权限，显示引导对话框")
                showUsageAccessPermissionDialog()
                return
            }

            if (!checkOverlayPermission()) {
                XLog.w("缺少悬浮窗权限，跳转到应用设置页面")
                openAppSettings()
                return
            }

            // 权限足够，直接启动homePackage进入管控状态
            // 不再需要设置管控状态，由WhitelistManager.openHomeApp()自动设置
            WhitelistManager.openHomeApp()
            finish() // 关闭MainActivity

        } catch (e: Exception) {
            XLog.e("处理进入管控状态请求失败", e)
            Toast.makeText(this, "操作失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }

    /** 简单判断是否 MIUI  */
    private fun isXiaomi(): Boolean {
        val manufacturer = Build.MANUFACTURER
        return manufacturer != null && manufacturer.equals("Xiaomi", ignoreCase = true)
    }

    /** 跳转到 MIUI 专属的应用权限编辑页，失败则退回到系统应用详情页  */
    private fun openMiuiPopupPermissionSettings() {
        try {
            MessageDialog.build()
                .setCancelable(false)
                .setTitle("小米特殊权限说明")
                .setMessage("请在设置中开启“允许后台弹出界面”权限，以保证应用能够正常正常使用。")
                .setOkButton("确认") { dialog, v ->
                    try {
                        val intent = Intent("miui.intent.action.APP_PERM_EDITOR")
                        intent.setClassName(
                            "com.miui.securitycenter",
                            "com.miui.permcenter.permissions.AppPermissionsEditorActivity"
                        )
                        intent.putExtra("extra_pkgname", getPackageName())
                        startActivity(intent)
                    } catch (e: ActivityNotFoundException) {
                        // MIUI 版本兼容或者非 MIUI 设备时，退到通用的应用详情设置
                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        val uri = Uri.fromParts("package", getPackageName(), null)
                        intent.setData(uri)
                        startActivity(intent)
                    }
                    false
                }
                .setCancelButton("取消") { dialog, v ->
                    false
                }
                .show()
        } catch (e: Exception) {
            XLog.e("显示“允许后台弹出界面”权限失败", e)
            Toast.makeText(this, "显示权限引导失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun openMiuiAutoStartSettings() {
        try {
            MessageDialog.build()
                .setCancelable(false)
                .setTitle("小米特殊权限说明")
                .setMessage("请在设置中开启“自启动”权限，以保证应用能够正常正常使用。")
                .setOkButton("确认") { dialog, v ->
                    try {
                        val intent = Intent()
                        intent.setComponent(
                            ComponentName(
                                "com.miui.securitycenter",
                                "com.miui.permcenter.autostart.AutoStartManagementActivity"
                            )
                        )
                        startActivity(intent)
                    } catch (e: ActivityNotFoundException) {
                        // 回退到通用应用详情
                        val fallback = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        fallback.setData(Uri.fromParts("package", getPackageName(), null))
                        startActivity(fallback)
                    }
                    false
                }
                .setCancelButton("取消") { dialog, v ->
                    false
                }
                .show()
        } catch (e: Exception) {
            XLog.e("显示“自启动”权限失败", e)
            Toast.makeText(this, "显示权限引导失败", Toast.LENGTH_SHORT).show()
        }
    }

    private fun openMiuiIgnorePowerSettings() {
        try {
            MessageDialog.build()
                .setCancelable(false)
                .setTitle("小米特殊权限说明")
                .setMessage("请在设置中开启“省电策略-无限制”权限，以保证应用能够正常正常使用。")
                .setOkButton("确认") { dialog, v ->
                    try {
                        val intent = Intent()
                        // 组件名可能因 MIUI 版本略有不同，可根据实际机型调整
                        intent.setComponent(
                            ComponentName(
                                "com.miui.powerkeeper",
                                "com.miui.powerkeeper.ui.HiddenAppsConfigActivity"
                            )
                        )
                        // 必需的两个参数：包名和应用名
                        intent.putExtra("package_name", getPackageName())
                        intent.putExtra("package_label", getString(R.string.app_name))
                        startActivity(intent)
                    } catch (e: ActivityNotFoundException) {
                        // 找不到时退到通用的应用详情页
                        val fallback = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        val uri = Uri.fromParts("package", getPackageName(), null)
                        fallback.setData(uri)
                        startActivity(fallback)
                    }
                    false
                }
                .setCancelButton("取消") { dialog, v ->
                    false
                }
                .show()
        } catch (e: Exception) {
            XLog.e("显示“省电策略-无限制”权限失败", e)
            Toast.makeText(this, "显示权限引导失败", Toast.LENGTH_SHORT).show()
        }
    }




    /**
     * 检查使用情况访问权限
     */
    private fun checkUsageAccessPermission(): Boolean {
        return try {
            AppUsageHelper.hasUsageAccessPermission(this)
        } catch (e: Exception) {
            XLog.e("检查使用情况访问权限失败", e)
            false
        }
    }

    /**
     * 检查悬浮窗权限
     */
    private fun checkOverlayPermission(): Boolean {
        return try {
            Settings.canDrawOverlays(this)
        } catch (e: Exception) {
            XLog.e("检查悬浮窗权限失败", e)
            false
        }
    }



    /**
     * 跳转到应用设置页面开启悬浮窗权限
     */
    private fun openAppSettings() {
        try {
            XLog.d("跳转到应用设置页面")
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            intent.data = "package:$packageName".toUri()
            startActivity(intent)
            Toast.makeText(this, "请开启悬浮窗权限（显示在其他应用的上层）", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            XLog.e("打开应用设置失败", e)
            Toast.makeText(this, "无法打开应用设置，请手动前往设置页面", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 取消状态监控协程
        statusUpdateJob?.cancel()
        activityScope.cancel()


    }

    /**
     * 处理标题点击事件 - 隐藏操作入口
     */
    private fun handleTitleClick() {
        val currentTime = System.currentTimeMillis()

        // 添加当前点击时间
        clickTimes.add(currentTime)

        // 移除超出时间窗口的点击记录
        clickTimes.removeAll { currentTime - it > clickTimeWindow }

        // 检查是否达到触发条件
        if (clickTimes.size >= requiredClicks) {
            XLog.d("检测到隐藏操作触发条件")
            clickTimes.clear() // 清空点击记录，防止重复触发
            showPasswordDialog()
        }
    }

    /**
     * 显示密码验证对话框
     */
    private fun showPasswordDialog() {
        InputDialog("管理员验证", "", "确定", "取消", "")
            .setInputHintText("请输入管理员密码")
            .setInputInfo(InputInfo().apply {
                inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_TEXT_VARIATION_PASSWORD
            })
            .setOkButton { _, _, inputStr ->
                if (inputStr == Constant.DEF_SUPER_ADMIN_PWD) {
                    showFirstWarningDialog()
                } else {
                    Toaster.showLong("密码错误")
                }
                false
            }
            .show()
    }

    /**
     * 显示第一次警告对话框
     */
    private fun showFirstWarningDialog() {
        MessageDialog.build()
            .setCancelable(false)
            .setTitle("⚠️ 危险操作警告")
            .setMessage("您即将解除设备管理者权限！\n\n此操作将会：\n• 失去设备管理控制能力\n• 无法使用应用锁定功能\n• 可能影响系统安全策略\n\n请确认您了解此操作的后果。")
            .setOkButton("我了解，继续") { _, _ ->
                showSecondWarningDialog()
                false
            }
            .setCancelButton("取消") { _, _ ->
                false
            }
            .show()
    }

    /**
     * 显示第二次警告对话框
     */
    private fun showSecondWarningDialog() {
        MessageDialog.build()
            .setCancelable(false)
            .setTitle("🚨 最终确认")
            .setMessage("这是最后一次确认！\n\n解除设备管理者权限后：\n• 应用将失去管控能力\n• 需要重新配置才能恢复功能\n• 此操作不可撤销\n\n您确定要继续吗？")
            .setOkButton("确定解除") { _, _ ->
                clearDeviceOwnerApp()
                false
            }
            .setCancelButton("取消") { _, _ ->
                false
            }
            .show()
    }

    /**
     * 解除设备管理者权限
     */
    private fun clearDeviceOwnerApp() {
        try {
            val devicePolicyManager = getSystemService(DEVICE_POLICY_SERVICE) as DevicePolicyManager
            val packageName = packageName

            XLog.d("开始解除设备管理者权限: $packageName")

            // 调用解除设备管理者权限的方法
            devicePolicyManager.clearDeviceOwnerApp(packageName)

            // 显示成功消息
            MessageDialog.build()
                .setCancelable(false)
                .setTitle("✅ 操作完成")
                .setMessage("设备管理者权限已成功解除。\n\n应用将重启以应用更改。")
                .setOkButton("确定") { _, _ ->
                    // 重启应用
                    restartApp()
                    false
                }
                .show()

            XLog.d("设备管理者权限解除成功")

        } catch (e: SecurityException) {
            XLog.e("解除设备管理者权限失败：权限不足", e)
            MessageDialog.build()
                .setTitle("❌ 操作失败")
                .setMessage("解除失败：权限不足\n\n可能原因：\n• 当前不是设备管理者\n• 系统限制此操作")
                .setOkButton("确定") { _, _ -> false }
                .show()
        } catch (e: Exception) {
            XLog.e("解除设备管理者权限失败", e)
            MessageDialog.build()
                .setTitle("❌ 操作失败")
                .setMessage("解除失败：${e.message}")
                .setOkButton("确定") { _, _ -> false }
                .show()
        }
    }

    /**
     * 重启应用
     */
    private fun restartApp() {
        try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            intent?.let {
                it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(it)
            }
            finish()
            android.os.Process.killProcess(android.os.Process.myPid())
        } catch (e: Exception) {
            XLog.e("重启应用失败", e)
            finish()
        }
    }

    // ==================== 重置设备码相关方法 ====================

    /**
     * 显示重置设备码的密码验证对话框
     */
    private fun showDeviceIdResetPasswordDialog() {
        InputDialog("管理员验证", "", "确定", "取消", "")
            .setInputHintText("请输入管理员密码")
            .setInputInfo(InputInfo().apply {
                inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_TEXT_VARIATION_PASSWORD
            })
            .setOkButton { _, _, inputStr ->
                if (inputStr == Constant.DEF_SUPER_ADMIN_PWD) {
                    showDeviceIdResetFirstWarningDialog()
                } else {
                    Toaster.showLong("密码错误")
                }
                false
            }
            .show()
    }

    /**
     * 显示重置设备码的第一次警告对话框
     */
    private fun showDeviceIdResetFirstWarningDialog() {
        MessageDialog.build()
            .setCancelable(false)
            .setTitle("⚠️ 重置设备码警告")
            .setMessage("您即将重置设备码！\n\n此操作将会：\n• 生成全新的设备标识码\n• 可能影响与服务器的关联\n• 需要重新进行设备认证\n\n请确认您了解此操作的后果。")
            .setOkButton("我了解，继续") { _, _ ->
                showDeviceIdResetSecondWarningDialog()
                false
            }
            .setCancelButton("取消") { _, _ ->
                false
            }
            .show()
    }

    /**
     * 显示重置设备码的第二次警告对话框
     */
    private fun showDeviceIdResetSecondWarningDialog() {
        MessageDialog.build()
            .setCancelable(false)
            .setTitle("🚨 最终确认")
            .setMessage("这是最后一次确认！\n\n重置设备码后：\n• 当前设备码将被永久替换\n• 可能需要重新配置服务器关联\n• 此操作不可撤销\n\n您确定要继续吗？")
            .setOkButton("确定重置") { _, _ ->
                resetDeviceId()
                false
            }
            .setCancelButton("取消") { _, _ ->
                false
            }
            .show()
    }

    /**
     * 执行重置设备码操作
     */
    private fun resetDeviceId() {
        try {
            XLog.d("开始重置设备码")

            // 获取旧的设备ID用于日志记录
            val oldDeviceId = UserManager.getDeviceId()
            XLog.d("旧设备码: $oldDeviceId")

            // 执行重置操作
            val newDeviceId = UserManager.refreshDeviceId()
            XLog.d("新设备码: $newDeviceId")

            // 显示成功消息
            MessageDialog.build()
                .setCancelable(false)
                .setTitle("✅ 重置完成")
                .setMessage("设备码已成功重置。\n\n新设备码：$newDeviceId\n\n请记录此设备码以备后续使用。")
                .setOkButton("确定") { _, _ ->
                    false
                }
                .show()

            XLog.d("设备码重置成功：$oldDeviceId -> $newDeviceId")

        } catch (e: Exception) {
            XLog.e("重置设备码失败", e)
            MessageDialog.build()
                .setTitle("❌ 重置失败")
                .setMessage("重置失败：${e.message}")
                .setOkButton("确定") { _, _ -> false }
                .show()
        }
    }

    /**
     * 启动时静默检查更新
     */
    private fun checkUpdateOnStartup() {
        // 延迟5秒后检查，避免影响启动速度
        activityScope.launch {
            kotlinx.coroutines.delay(5000)

            try {
                XLog.d("开始启动时更新检查")

                UpdateManager.checkForceUpdate(this@MainActivity) { isForceUpdate ->
                    if (isForceUpdate) {
                        XLog.w("检测到强制更新")
                        runOnUiThread {
                            Toaster.show("检测到重要更新，请及时更新应用")
                            // 强制更新时自动弹出更新对话框
                            UpdateManager.checkUpdate(this@MainActivity, showNoUpdateToast = false)
                        }
                    } else {
                        XLog.d("启动检查：无强制更新")
                    }
                }
            } catch (e: Exception) {
                XLog.e("启动时检查更新失败", e)
            }
        }
    }

    /**
     * 手动检查应用更新
     */
    private fun checkForUpdates() {
        try {
            XLog.d("开始手动检查应用更新")

            // 显示检查中的提示
            Toaster.show("正在检查更新...")

            // 调用UpdateManager检查更新
            UpdateManager.checkUpdate(this, showNoUpdateToast = true)

        } catch (e: Exception) {
            XLog.e("检查更新失败", e)
            Toaster.show("检查更新失败：${e.message}")
        }
    }

    /**
     * 显示使用情况访问权限获取引导对话框
     */
    private fun showUsageAccessPermissionDialog() {
        try {
            MessageDialog.build()
                .setCancelable(false)
                .setTitle("开启使用情况访问权限")
                .setMessage("需要使用情况访问权限来监控应用使用情况，实现数据清理功能。\n\n点击\"确认\"将跳转到使用情况访问设置页面，请找到\"Time Controller\"并开启权限。")
                .setOkButton("确认") { dialog, v ->
                    XLog.d("用户确认开启使用情况访问权限")
                    openUsageAccessSettings()
                    false
                }
                .setCancelButton("取消") { dialog, v ->
                    XLog.d("用户取消开启使用情况访问权限")
                    false
                }
                .show()
        } catch (e: Exception) {
            XLog.e("显示使用情况访问权限对话框失败", e)
            Toast.makeText(this, "显示权限引导失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 打开使用情况访问设置页面
     */
    private fun openUsageAccessSettings() {
        try {
            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
            startActivity(intent)
        } catch (e: Exception) {
            XLog.e("打开使用情况访问设置失败", e)
            Toast.makeText(this, "无法打开设置页面", Toast.LENGTH_SHORT).show()
        }
    }
}
