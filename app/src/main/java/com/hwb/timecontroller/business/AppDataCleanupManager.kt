package com.hwb.timecontroller.business

import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import com.hwb.timecontroller.AppDeviceAdminReceiver
import com.hwb.timecontroller.MyApplication
import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.constant.Constant
import com.hwb.timecontroller.utils.AppUsageHelper
import com.tencent.mmkv.MMKV
import com.elvishew.xlog.XLog
import java.util.concurrent.CopyOnWriteArraySet
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.Executor

/**
 * 应用数据清理记录管理器
 * 记录倒计时结束后需要清理数据的应用包名
 * 
 * Author: huangwubin
 * Date: 2025/7/8
 */
object AppDataCleanupManager {

    private const val TAG = "AppDataCleanupManager"

    // MMKV存储键
    private const val KEY_CLEANUP_TARGET_APPS = "cleanup_target_apps"

    // MMKV实例
    private val mmkv: MMKV by lazy { MMKV.defaultMMKV() }

    // 内存缓存，使用线程安全的Set
    private val cleanupTargetApps = CopyOnWriteArraySet<String>()

    // 初始化标志
    private var isInitialized = false

    // Device Policy Manager 相关
    private val context: Context get() = MyApplication.myApp
    private val devicePolicyManager: DevicePolicyManager by lazy {
        context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    }
    private val adminComponent: ComponentName by lazy {
        ComponentName(context, AppDeviceAdminReceiver::class.java)
    }

    // 协程作用域
    private val cleanupScope = CoroutineScope(Dispatchers.IO)
    
    /**
     * 初始化管理器
     * 从存储中加载清理目标应用记录
     */
    fun initialize() {
        if (isInitialized) {
            return
        }
        
        try {
            // 从MMKV加载清理目标应用记录
            val savedApps = mmkv.getStringSet(KEY_CLEANUP_TARGET_APPS, emptySet()) ?: emptySet()
            cleanupTargetApps.clear()
            cleanupTargetApps.addAll(savedApps)
            
            isInitialized = true
            XLog.d("初始化完成，加载了 ${cleanupTargetApps.size} 个清理目标应用记录")
            
        } catch (e: Exception) {
            XLog.e("初始化失败", e)
        }
    }
    
    /**
     * 记录需要清理数据的应用
     * @param packageName 应用包名
     */
    fun recordCleanupTargetApp(packageName: String) {
        try {
            // 确保已初始化
            if (!isInitialized) {
                initialize()
            }
            
            // 只在管控模式下记录
            if (!AdminManager.isGovernanceState) {
                XLog.d("非管控模式，跳过记录: $packageName")
                return
            }
            
            // 检查是否需要记录此应用
            if (!shouldRecordApp(packageName)) {
                XLog.d("跳过不需要记录的应用: $packageName")
                return
            }
            
            // 添加到内存缓存
            val isNewRecord = cleanupTargetApps.add(packageName)
            
            if (isNewRecord) {
                // 保存到MMKV
                saveToStorage()
                XLog.d("记录新的清理目标应用: $packageName")
            } else {
                XLog.d("清理目标应用已存在记录: $packageName")
            }
            
        } catch (e: Exception) {
            XLog.e("记录清理目标应用失败: $packageName", e)
        }
    }
    
    /**
     * 获取所有需要清理数据的应用包名
     * @return 应用包名集合
     */
    fun getCleanupTargetApps(): Set<String> {
        if (!isInitialized) {
            initialize()
        }
        return cleanupTargetApps.toSet()
    }

    /**
     * 基于时间范围获取需要清理的应用
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 需要清理的应用包名集合
     */
    fun getCleanupTargetAppsByTimeRange(startTime: Long, endTime: Long): Set<String> {
        return try {
            // 检查是否有使用情况访问权限
            if (!AppUsageHelper.hasUsageAccessPermission(context)) {
                XLog.w("没有使用情况访问权限，无法获取应用使用记录")
                return emptySet()
            }

            // 获取时间范围内启动的应用
            val excludedPackages = listOf(
                context.packageName, // 本应用
                Constant.homePackage // 主页应用
            )

            val launchedApps = AppUsageHelper.getLaunchedAppsBetween(
                context, startTime, endTime, excludedPackages
            )

            XLog.d("时间范围内启动的应用数量: ${launchedApps.size}")
            launchedApps

        } catch (e: Exception) {
            XLog.e("获取时间范围内的应用失败", e)
            emptySet()
        }
    }
    
    /**
     * 检查指定应用是否为清理目标
     * @param packageName 应用包名
     * @return 是否为清理目标
     */
    fun isCleanupTarget(packageName: String): Boolean {
        if (!isInitialized) {
            initialize()
        }
        return cleanupTargetApps.contains(packageName)
    }
    
    /**
     * 获取清理目标应用数量
     * @return 应用数量
     */
    fun getCleanupTargetCount(): Int {
        if (!isInitialized) {
            initialize()
        }
        return cleanupTargetApps.size
    }
    
    /**
     * 从清理目标中移除指定应用
     * @param packageName 应用包名
     */
    fun removeFromCleanupTargets(packageName: String) {
        try {
            if (!isInitialized) {
                initialize()
            }
            
            val removed = cleanupTargetApps.remove(packageName)
            if (removed) {
                saveToStorage()
                XLog.d("从清理目标中移除应用: $packageName")
            }
            
        } catch (e: Exception) {
            XLog.e("从清理目标中移除应用失败: $packageName", e)
        }
    }
    
    /**
     * 清除所有清理目标记录
     */
    fun clearAllCleanupTargets() {
        try {
            cleanupTargetApps.clear()
            mmkv.remove(KEY_CLEANUP_TARGET_APPS)
            XLog.d("清除所有清理目标记录")

        } catch (e: Exception) {
            XLog.e("清除所有清理目标记录失败", e)
        }
    }

    /**
     * 基于时间范围清理应用数据
     * 使用Device Owner权限清理在指定时间范围内启动的应用数据
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param onProgress 进度回调 (当前应用包名, 当前索引, 总数量)
     * @param onComplete 完成回调 (成功数量, 失败数量, 失败的应用列表)
     */
    fun cleanupAppDataByTimeRange(
        startTime: Long,
        endTime: Long,
        onProgress: ((packageName: String, current: Int, total: Int) -> Unit)? = null,
        onComplete: ((successCount: Int, failureCount: Int, failedApps: List<String>) -> Unit)? = null
    ) {
        cleanupScope.launch {
            try {
                // 检查Device Owner权限
                if (!isDeviceOwner()) {
                    XLog.w("没有Device Owner权限，无法清理应用数据")
                    withContext(Dispatchers.Main) {
                        onComplete?.invoke(0, 0, emptyList())
                    }
                    return@launch
                }

                // 获取时间范围内启动的应用
                val appsToCleanup = getCleanupTargetAppsByTimeRange(startTime, endTime).toList()
                if (appsToCleanup.isEmpty()) {
                    XLog.d("时间范围内没有需要清理的应用")
                    withContext(Dispatchers.Main) {
                        onComplete?.invoke(0, 0, emptyList())
                    }
                    return@launch
                }

                XLog.i("开始清理时间范围内的 ${appsToCleanup.size} 个应用数据")

                var successCount = 0
                var failureCount = 0
                val failedApps = mutableListOf<String>()

                appsToCleanup.forEachIndexed { index, packageName ->
                    try {
                        // 通知进度
                        withContext(Dispatchers.Main) {
                            onProgress?.invoke(packageName, index + 1, appsToCleanup.size)
                        }

                        // 清理单个应用数据
                        val success = cleanupSingleAppData(packageName)
                        if (success) {
                            successCount++
                            XLog.d("成功清理应用数据: $packageName")
                        } else {
                            failureCount++
                            failedApps.add(packageName)
                            XLog.w("清理应用数据失败: $packageName")
                        }

                        // 添加延迟避免过于频繁的操作
                        kotlinx.coroutines.delay(500)

                    } catch (e: Exception) {
                        failureCount++
                        failedApps.add(packageName)
                        XLog.e("清理应用数据异常: $packageName", e)
                    }
                }

                XLog.i("时间范围应用数据清理完成 - 成功: $successCount, 失败: $failureCount")

                // 通知完成
                withContext(Dispatchers.Main) {
                    onComplete?.invoke(successCount, failureCount, failedApps)
                }

            } catch (e: Exception) {
                XLog.e("清理时间范围应用数据过程发生异常", e)
                withContext(Dispatchers.Main) {
                    onComplete?.invoke(0, 0, emptyList())
                }
            }
        }
    }

    /**
     * 清理所有记录的应用数据
     * 使用Device Owner权限清理应用的所有数据
     * @param onProgress 进度回调 (当前应用包名, 当前索引, 总数量)
     * @param onComplete 完成回调 (成功数量, 失败数量, 失败的应用列表)
     */
    fun cleanupAllAppData(
        onProgress: ((packageName: String, current: Int, total: Int) -> Unit)? = null,
        onComplete: ((successCount: Int, failureCount: Int, failedApps: List<String>) -> Unit)? = null
    ) {
        cleanupScope.launch {
            try {
                // 确保已初始化
                if (!isInitialized) {
                    initialize()
                }

                // 检查Device Owner权限
                if (!isDeviceOwner()) {
                    XLog.w("没有Device Owner权限，无法清理应用数据")
                    withContext(Dispatchers.Main) {
                        onComplete?.invoke(0, 0, emptyList())
                    }
                    return@launch
                }

                val appsToCleanup = cleanupTargetApps.toList()
                if (appsToCleanup.isEmpty()) {
                    XLog.d("没有需要清理的应用")
                    withContext(Dispatchers.Main) {
                        onComplete?.invoke(0, 0, emptyList())
                    }
                    return@launch
                }

                XLog.i("开始清理 ${appsToCleanup.size} 个应用的数据")

                var successCount = 0
                var failureCount = 0
                val failedApps = mutableListOf<String>()

                appsToCleanup.forEachIndexed { index, packageName ->
                    try {
                        // 通知进度
                        withContext(Dispatchers.Main) {
                            onProgress?.invoke(packageName, index + 1, appsToCleanup.size)
                        }

                        // 清理单个应用数据
                        val success = cleanupSingleAppData(packageName)
                        if (success) {
                            successCount++
                            XLog.d("成功清理应用数据: $packageName")
                        } else {
                            failureCount++
                            failedApps.add(packageName)
                            XLog.w("清理应用数据失败: $packageName")
                        }

                        // 添加延迟避免过于频繁的操作
                        kotlinx.coroutines.delay(500)

                    } catch (e: Exception) {
                        failureCount++
                        failedApps.add(packageName)
                        XLog.e("清理应用数据异常: $packageName", e)
                    }
                }

                XLog.i("应用数据清理完成 - 成功: $successCount, 失败: $failureCount")

                // 通知完成
                withContext(Dispatchers.Main) {
                    onComplete?.invoke(successCount, failureCount, failedApps)
                }

            } catch (e: Exception) {
                XLog.e("清理应用数据过程发生异常", e)
                withContext(Dispatchers.Main) {
                    onComplete?.invoke(0, 0, emptyList())
                }
            }
        }
    }

    /**
     * 清理所有记录的应用数据（简化版本）
     * 使用Device Owner权限清理应用的所有数据，不提供进度回调
     * @return 清理结果信息
     */
    suspend fun cleanupAllAppDataSimple(): CleanupResult {
        return withContext(Dispatchers.IO) {
            try {
                // 确保已初始化
                if (!isInitialized) {
                    initialize()
                }

                // 检查Device Owner权限
                if (!isDeviceOwner()) {
                    XLog.w("没有Device Owner权限，无法清理应用数据")
                    return@withContext CleanupResult(0, 0, emptyList(), "没有Device Owner权限")
                }

                val appsToCleanup = cleanupTargetApps.toList()
                if (appsToCleanup.isEmpty()) {
                    XLog.d("没有需要清理的应用")
                    return@withContext CleanupResult(0, 0, emptyList(), "没有需要清理的应用")
                }

                XLog.i("开始清理 ${appsToCleanup.size} 个应用的数据")

                var successCount = 0
                var failureCount = 0
                val failedApps = mutableListOf<String>()

                appsToCleanup.forEach { packageName ->
                    try {
                        val success = cleanupSingleAppData(packageName)
                        if (success) {
                            successCount++
                        } else {
                            failureCount++
                            failedApps.add(packageName)
                        }
                        // 添加延迟避免过于频繁的操作
                        kotlinx.coroutines.delay(500)
                    } catch (e: Exception) {
                        failureCount++
                        failedApps.add(packageName)
                        XLog.e("清理应用数据异常: $packageName", e)
                    }
                }

                val message = "清理完成 - 成功: $successCount, 失败: $failureCount"
                XLog.i(message)

                CleanupResult(successCount, failureCount, failedApps, message)

            } catch (e: Exception) {
                XLog.e("清理应用数据过程发生异常", e)
                CleanupResult(0, 0, emptyList(), "清理过程发生异常: ${e.message}")
            }
        }
    }

    /**
     * 清理结果数据类
     */
    data class CleanupResult(
        val successCount: Int,
        val failureCount: Int,
        val failedApps: List<String>,
        val message: String
    )

    /**
     * 判断是否需要记录此应用
     * 跳过本应用包名和homePackage，其他应用都记录
     */
    private fun shouldRecordApp(packageName: String): Boolean {
        // 跳过本应用包名
        if (packageName == MyApplication.myApp.packageName) {
            return false
        }
        
        // 跳过homePackage
        if (packageName == Constant.homePackage) {
            return false
        }
        
        // 其他应用都记录
        return true
    }
    
    /**
     * 清理单个应用的数据
     * @param packageName 应用包名
     * @return 是否成功
     */
    private suspend fun cleanupSingleAppData(packageName: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                XLog.d("开始清理应用数据: $packageName")

                // 检查应用是否存在
                if (!isAppInstalled(packageName)) {
                    XLog.w("应用未安装，跳过清理: $packageName")
                    return@withContext true // 应用不存在视为成功
                }

                // 使用Device Owner权限清理应用数据
                devicePolicyManager.clearApplicationUserData(
                    adminComponent,
                    packageName,
                    { runnable -> runnable.run() }, // 同步执行器
                    { packageName, succeeded ->
                        // 清理完成回调
                        XLog.d("应用数据清理回调: $packageName, 成功: $succeeded")
                    }
                )

                val success = true // clearApplicationUserData 是 void 方法，假设调用成功

                if (success) {
                    XLog.d("应用数据清理请求已发送: $packageName")
                } else {
                    XLog.w("应用数据清理请求失败: $packageName")
                }

                return@withContext success

            } catch (e: Exception) {
                XLog.e("清理应用数据异常: $packageName", e)
                return@withContext false
            }
        }
    }

    /**
     * 检查应用是否已安装
     * @param packageName 应用包名
     * @return 是否已安装
     */
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            context.packageManager.getApplicationInfo(packageName, 0)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查是否具有Device Owner权限
     * @return 是否为Device Owner
     */
    private fun isDeviceOwner(): Boolean {
        return try {
            devicePolicyManager.isDeviceOwnerApp(context.packageName)
        } catch (e: Exception) {
            XLog.e("检查Device Owner权限失败", e)
            false
        }
    }

    /**
     * 保存到存储
     */
    private fun saveToStorage() {
        try {
            mmkv.putStringSet(KEY_CLEANUP_TARGET_APPS, cleanupTargetApps.toSet())
        } catch (e: Exception) {
            XLog.e("保存到存储失败", e)
        }
    }
}
