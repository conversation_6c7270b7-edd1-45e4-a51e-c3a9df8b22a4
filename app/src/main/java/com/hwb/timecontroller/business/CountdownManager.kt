package com.hwb.timecontroller.business

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 倒计时数据管理类
 * 使用StateFlow管理倒计时时间，通过剩余时长判断状态
 * <p>
 * Author:huang<PERSON>bin
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/1: First created this class.
 * 2025/7/11: 移除CountdownState枚举，简化为基于剩余时长的判断
 */
object CountdownManager {

    private const val KEY_START_TIME = "countdown_start_time"

    /**
     * 倒计时数据类
     */
    data class CountdownData(
        val totalDurationMillis: Long = 0L,
        val remainingTimeMillis: Long = 0L,
        val isDeviceLocked: Boolean = false
    )

    // 私有的可变StateFlow
    private val _countdownData = MutableStateFlow(CountdownData())

    // 公开的只读StateFlow
    val countdownData: StateFlow<CountdownData> = _countdownData.asStateFlow()

    /**
     * 开始倒计时
     * @param durationMillis 倒计时总时长（毫秒）
     */
    fun startCountdown(durationMillis: Long) {
        _countdownData.value = CountdownData(
            totalDurationMillis = durationMillis,
            remainingTimeMillis = durationMillis,
            isDeviceLocked = false
        )
    }

    /**
     * 更新剩余时间
     * @param remainingMillis 剩余时间（毫秒）
     */
    fun updateRemainingTime(remainingMillis: Long) {
        val currentData = _countdownData.value
        _countdownData.value = currentData.copy(
            remainingTimeMillis = remainingMillis
        )
    }

    /**
     * 倒计时结束
     */
    fun finishCountdown() {
        val currentData = _countdownData.value
        _countdownData.value = currentData.copy(
            remainingTimeMillis = 0L,
            isDeviceLocked = true
        )
    }

    /**
     * 取消倒计时（设置为结束状态）
     */
    fun cancelCountdown() {
        val currentData = _countdownData.value
        _countdownData.value = currentData.copy(
            remainingTimeMillis = 0L,
            isDeviceLocked = false
        )
    }

    /**
     * 重置倒计时状态
     */
    fun resetCountdown() {
        _countdownData.value = CountdownData()
    }

    /**
     * 设置设备锁定状态
     * @param isLocked 是否锁定
     */
    fun setDeviceLocked(isLocked: Boolean) {
        val currentData = _countdownData.value
        _countdownData.value = currentData.copy(
            isDeviceLocked = isLocked
        )
    }

    /**
     * 获取剩余时间
     */
    fun getRemainingTime(): Long {
        return _countdownData.value.remainingTimeMillis
    }

    /**
     * 获取总时长
     */
    fun getTotalDuration(): Long {
        return _countdownData.value.totalDurationMillis
    }

    /**
     * 检查设备是否锁定
     */
    fun isDeviceLocked(): Boolean {
        return _countdownData.value.isDeviceLocked
    }

    /**
     * 检查倒计时是否正在运行（基于剩余时长判断）
     */
    fun isCountdownRunning(): Boolean {
        return _countdownData.value.remainingTimeMillis > 0
    }

    /**
     * 检查倒计时是否已结束（基于剩余时长判断）
     */
    fun isCountdownFinished(): Boolean {
        return _countdownData.value.remainingTimeMillis <= 0
    }


}