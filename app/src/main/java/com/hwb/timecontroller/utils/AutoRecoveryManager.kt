package com.hwb.timecontroller.utils

import android.content.Context
import android.content.Intent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog
import com.hwb.timecontroller.service.FloatingWindowService
import com.hwb.timecontroller.business.CountdownManager

/**
 * 自动恢复机制管理器
 * 监控系统状态并在异常时自动恢复
 */
class AutoRecoveryManager(
    private val context: Context,
    private val systemMonitor: SystemMonitor
) {

    companion object {
        private const val RECOVERY_CHECK_INTERVAL_MS = 10000L // 10秒检查一次
        private const val MAX_RECOVERY_ATTEMPTS = 3 // 最大恢复尝试次数
        private const val RECOVERY_COOLDOWN_MS = 30000L // 恢复冷却时间30秒

        private const val LOG_CLEANUP_INTERVAL_MS = 24 * 60 * 60 * 1000L // 24小时清理一次日志
        private const val LOG_RETENTION_DAYS = 7 // 日志保留7天
    }

    // 协程作用域
    private val recoveryScope = CoroutineScope(Dispatchers.Main + Job())
    private var recoveryJob: Job? = null
    private var logCleanupJob: Job? = null

    // 恢复状态跟踪
    private var lastRecoveryTime = 0L
    private var recoveryAttempts = 0
    private var isRecoveryInProgress = false
    private var lastLogCleanupTime = 0L


    /**
     * 启动自动恢复监控
     */
    fun startAutoRecovery() {
        try {
            XLog.d("启动自动恢复监控")

            recoveryJob = recoveryScope.launch {
                while (true) {
                    try {
                        checkAndRecover()
                    } catch (e: Exception) {
                        XLog.e("自动恢复检查出错", e)
                    }

                    delay(RECOVERY_CHECK_INTERVAL_MS)
                }
            }

            // 启动日志清理任务
            startLogCleanup()

        } catch (e: Exception) {
            XLog.e("启动自动恢复监控失败", e)
        }
    }

    /**
     * 停止自动恢复监控
     */
    fun stopAutoRecovery() {
        try {
            XLog.d("停止自动恢复监控")

            recoveryJob?.cancel()
            logCleanupJob?.cancel()
            recoveryScope.cancel()

        } catch (e: Exception) {
            XLog.e("停止自动恢复监控失败", e)
        }
    }

    /**
     * 检查并执行恢复操作
     */
    private suspend fun checkAndRecover() {
        if (isRecoveryInProgress) {
            XLog.d("恢复操作正在进行中，跳过本次检查")
            return
        }

        // 检查是否在冷却期内
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastRecoveryTime < RECOVERY_COOLDOWN_MS) {
            return
        }

        // 检查是否超过最大恢复尝试次数
        if (recoveryAttempts >= MAX_RECOVERY_ATTEMPTS) {
            XLog.w("已达到最大恢复尝试次数，停止自动恢复")
            return
        }

        val systemStatus = systemMonitor.getCurrentStatus()
        
        // 检查各种异常情况并尝试恢复
        if (needsRecovery(systemStatus)) {
            performRecovery(systemStatus)
        }
    }

    /**
     * 判断是否需要恢复
     */
    private fun needsRecovery(systemStatus: SystemMonitor.SystemStatus): Boolean {
        // 检查关键服务是否异常
        val hasOverlayPermission = systemStatus.hasOverlayPermission
        val isDeviceOwner = systemStatus.isDeviceOwner

        // 如果是设备所有者但悬浮窗权限丢失，需要恢复
        if (isDeviceOwner && !hasOverlayPermission) {
            XLog.w("检测到悬浮窗权限异常：设备所有者权限存在但悬浮窗权限丢失")
            return true
        }
        
        // 如果有权限但悬浮窗服务未运行，需要恢复
        if (hasOverlayPermission && !systemStatus.isFloatingWindowServiceRunning) {
            XLog.w("检测到悬浮窗服务异常：有权限但服务未运行")
            return true
        }
        
        // 检查倒计时状态异常（基于剩余时长判断）
        if (CountdownManager.isCountdownRunning() && !systemStatus.isCountdownServiceRunning) {
            XLog.w("检测到倒计时服务异常：倒计时正在运行但服务未运行")
            return true
        }
        
        return false
    }

    /**
     * 执行恢复操作
     */
    private suspend fun performRecovery(systemStatus: SystemMonitor.SystemStatus) {
        try {
            isRecoveryInProgress = true
            lastRecoveryTime = System.currentTimeMillis()
            recoveryAttempts++
            
            XLog.i("开始执行自动恢复操作 (尝试次数: $recoveryAttempts)")
            
            // 恢复悬浮窗权限
            if (systemStatus.isDeviceOwner && !systemStatus.hasOverlayPermission) {
                recoverOverlayPermission()
            }
            
            // 恢复悬浮窗服务
            if (systemStatus.hasOverlayPermission && !systemStatus.isFloatingWindowServiceRunning) {
                recoverFloatingWindowService()
            }
            
            // 恢复倒计时服务（使用统一的判断方法）
            if (CountdownManager.isCountdownRunning() && !systemStatus.isCountdownServiceRunning) {
                recoverCountdownService()
            }
            
            // 等待恢复操作生效
            delay(3000)
            
            // 验证恢复结果
            systemMonitor.refreshStatus()
            val newStatus = systemMonitor.getCurrentStatus()
            
            if (!needsRecovery(newStatus)) {
                XLog.i("自动恢复成功")
                recoveryAttempts = 0 // 重置恢复尝试次数
            } else {
                XLog.w("自动恢复未完全成功，将在下次检查时重试")
            }
            
        } catch (e: Exception) {
            XLog.e("执行自动恢复操作失败", e)
        } finally {
            isRecoveryInProgress = false
        }
    }



    /**
     * 恢复悬浮窗权限
     */
    private suspend fun recoverOverlayPermission() {
        try {
            XLog.i("尝试恢复悬浮窗权限")
            
            // 这里可以尝试通过设备所有者权限自动授予悬浮窗权限
            val deviceOwnerManager = DeviceOwnerPermissionManager(context)
            val granted = deviceOwnerManager.grantOverlayPermission()
            
            if (granted) {
                XLog.i("悬浮窗权限恢复成功")
            } else {
                XLog.w("悬浮窗权限恢复失败")
            }
            
        } catch (e: Exception) {
            XLog.e("恢复悬浮窗权限失败", e)
        }
    }

    /**
     * 恢复悬浮窗服务
     */
    private suspend fun recoverFloatingWindowService() {
        try {
            XLog.i("尝试恢复悬浮窗服务")
            
            FloatingWindowService.start(context)
            
            // 等待服务启动
            delay(2000)
            
            XLog.i("悬浮窗服务恢复完成")
            
        } catch (e: Exception) {
            XLog.e("恢复悬浮窗服务失败", e)
        }
    }

    /**
     * 恢复倒计时服务
     */
    private suspend fun recoverCountdownService() {
        try {
            XLog.i("尝试恢复倒计时服务")
            
            // 获取当前倒计时状态
            val remainingTime = CountdownManager.getRemainingTime()
            
            if (CountdownManager.isCountdownRunning()) {
                val intent = Intent(context, com.hwb.timecontroller.service.CountdownService::class.java)
                intent.putExtra("duration", remainingTime)
                context.startService(intent)
                
                XLog.i("倒计时服务恢复完成，剩余时间: ${remainingTime}ms")
            }
            
        } catch (e: Exception) {
            XLog.e("恢复倒计时服务失败", e)
        }
    }

    /**
     * 手动触发恢复检查
     */
    fun triggerRecoveryCheck() {
        recoveryScope.launch {
            try {
                XLog.d("手动触发恢复检查")
                checkAndRecover()
            } catch (e: Exception) {
                XLog.e("手动恢复检查失败", e)
            }
        }
    }

    /**
     * 重置恢复状态
     */
    fun resetRecoveryState() {
        recoveryAttempts = 0
        lastRecoveryTime = 0L
        isRecoveryInProgress = false

        XLog.d("恢复状态已重置")
    }

    /**
     * 获取恢复状态信息
     */
    fun getRecoveryInfo(): String {
        return "恢复尝试次数: $recoveryAttempts/$MAX_RECOVERY_ATTEMPTS, " +
               "上次恢复时间: ${if (lastRecoveryTime > 0) lastRecoveryTime else "无"}, " +
               "恢复进行中: $isRecoveryInProgress"
    }

    /**
     * 启动日志清理任务
     */
    private fun startLogCleanup() {
        logCleanupJob = recoveryScope.launch {
            while (true) {
                try {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastLogCleanupTime >= LOG_CLEANUP_INTERVAL_MS) {
                        performLogCleanup()
                        lastLogCleanupTime = currentTime
                    }
                } catch (e: Exception) {
                    XLog.e("日志清理任务出错", e)
                }

                delay(LOG_CLEANUP_INTERVAL_MS)
            }
        }
    }

    /**
     * 执行日志清理
     */
    private fun performLogCleanup() {
        try {
            XLog.d("开始清理过期日志")

            val logDir = context.getExternalFilesDir("logs")
            if (logDir != null && logDir.exists()) {
                val cutoffTime = System.currentTimeMillis() - (LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000L)

                logDir.listFiles()?.forEach { file ->
                    if (file.isFile && file.lastModified() < cutoffTime) {
                        if (file.delete()) {
                            XLog.d("删除过期日志文件: ${file.name}")
                        } else {
                            XLog.w("删除日志文件失败: ${file.name}")
                        }
                    }
                }

                XLog.d("日志清理完成")
            } else {
                XLog.d("日志目录不存在，跳过清理")
            }

        } catch (e: Exception) {
            XLog.e("执行日志清理失败", e)
        }
    }

    /**
     * 手动触发日志清理
     */
    fun triggerLogCleanup() {
        recoveryScope.launch {
            try {
                XLog.d("手动触发日志清理")
                performLogCleanup()
            } catch (e: Exception) {
                XLog.e("手动日志清理失败", e)
            }
        }
    }
}
