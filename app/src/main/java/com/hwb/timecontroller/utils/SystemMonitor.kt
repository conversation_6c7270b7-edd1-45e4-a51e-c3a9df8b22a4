package com.hwb.timecontroller.utils

import android.app.ActivityManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 系统状态监控管理器
 * 监控服务状态、权限状态、性能指标等
 */
class SystemMonitor(private val context: Context) {

    companion object {
        private const val MONITOR_INTERVAL_MS = 15000L // 15秒监控一次（减少频率）
        private const val PERFORMANCE_MONITOR_INTERVAL_MS = 30000L // 30秒性能监控一次
    }

    // 协程作用域
    private val monitorScope = CoroutineScope(Dispatchers.IO + Job())
    private var serviceMonitorJob: Job? = null
    private var permissionMonitorJob: Job? = null
    private var performanceMonitorJob: Job? = null

    // 系统状态数据类
    data class SystemStatus(
        val hasOverlayPermission: Boolean = false,
        val isDeviceOwner: Boolean = false,
        val isFloatingWindowServiceRunning: Boolean = false,
        val isCountdownServiceRunning: Boolean = false,
        val lastUpdateTime: Long = System.currentTimeMillis()
    )

    // 私有的可变StateFlow
    private val _systemStatus = MutableStateFlow(SystemStatus())

    // 公开的只读StateFlow
    val systemStatus: StateFlow<SystemStatus> = _systemStatus.asStateFlow()

    /**
     * 开始监控
     */
    fun startMonitoring() {
        try {
            XLog.d("开始系统状态监控")
            
            // 启动服务状态监控
            startServiceMonitoring()
            
            // 启动权限状态监控
            startPermissionMonitoring()
            
            // 启动性能监控
            startPerformanceMonitoring()
            
        } catch (e: Exception) {
            XLog.e("启动系统监控失败", e)
        }
    }

    /**
     * 停止监控
     */
    fun stopMonitoring() {
        try {
            XLog.d("停止系统状态监控")
            
            serviceMonitorJob?.cancel()
            permissionMonitorJob?.cancel()
            performanceMonitorJob?.cancel()
            monitorScope.cancel()
            
        } catch (e: Exception) {
            XLog.e("停止系统监控失败", e)
        }
    }

    /**
     * 启动服务状态监控
     */
    private fun startServiceMonitoring() {
        serviceMonitorJob = monitorScope.launch {
            while (true) {
                try {
                    val currentStatus = _systemStatus.value
                    val newStatus = currentStatus.copy(
                        isFloatingWindowServiceRunning = checkServiceRunning("FloatingWindowService"),
                        isCountdownServiceRunning = checkServiceRunning("CountdownService"),
                        lastUpdateTime = System.currentTimeMillis()
                    )
                    
                    if (newStatus != currentStatus) {
                        _systemStatus.value = newStatus
                        logServiceStatusChanges(currentStatus, newStatus)
                    }
                    
                } catch (e: Exception) {
                    XLog.e("服务状态监控出错", e)
                }
                
                delay(MONITOR_INTERVAL_MS)
            }
        }
    }

    /**
     * 启动权限状态监控
     */
    private fun startPermissionMonitoring() {
        permissionMonitorJob = monitorScope.launch {
            while (true) {
                try {
                    val currentStatus = _systemStatus.value
                    val newStatus = currentStatus.copy(
                        hasOverlayPermission = checkOverlayPermission(),
                        isDeviceOwner = checkDeviceOwnerStatus(),
                        lastUpdateTime = System.currentTimeMillis()
                    )
                    
                    if (newStatus != currentStatus) {
                        _systemStatus.value = newStatus
                        logPermissionStatusChanges(currentStatus, newStatus)
                    }
                    
                } catch (e: Exception) {
                    XLog.e("权限状态监控出错", e)
                }
                
                delay(MONITOR_INTERVAL_MS)
            }
        }
    }

    /**
     * 启动性能监控
     */
    private fun startPerformanceMonitoring() {
        performanceMonitorJob = monitorScope.launch {
            while (true) {
                try {
                    val currentStatus = _systemStatus.value

                    val newStatus = currentStatus.copy(
                        lastUpdateTime = System.currentTimeMillis()
                    )

                    _systemStatus.value = newStatus

                } catch (e: Exception) {
                    XLog.e("性能监控出错", e)
                }

                delay(PERFORMANCE_MONITOR_INTERVAL_MS)
            }
        }
    }



    /**
     * 检查悬浮窗权限
     */
    private fun checkOverlayPermission(): Boolean {
        return try {
            Settings.canDrawOverlays(context)
        } catch (e: Exception) {
            XLog.e("检查悬浮窗权限失败", e)
            false
        }
    }

    /**
     * 检查设备所有者状态
     */
    private fun checkDeviceOwnerStatus(): Boolean {
        return try {
            DeviceOwnerPermissionManager(context).isDeviceOwner()
        } catch (e: Exception) {
            XLog.e("检查设备所有者状态失败", e)
            false
        }
    }

    /**
     * 检查服务是否运行
     */
    private fun checkServiceRunning(serviceName: String): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            services.any { serviceInfo ->
                serviceInfo.service.className.contains(serviceName)
            }
        } catch (e: Exception) {
            XLog.e("检查服务运行状态失败: $serviceName", e)
            false
        }
    }



    /**
     * 记录服务状态变化
     */
    private fun logServiceStatusChanges(oldStatus: SystemStatus, newStatus: SystemStatus) {

        
        if (oldStatus.isFloatingWindowServiceRunning != newStatus.isFloatingWindowServiceRunning) {
            XLog.i("悬浮窗服务状态变化: ${oldStatus.isFloatingWindowServiceRunning} -> ${newStatus.isFloatingWindowServiceRunning}")
        }
        
        if (oldStatus.isCountdownServiceRunning != newStatus.isCountdownServiceRunning) {
            XLog.i("倒计时服务状态变化: ${oldStatus.isCountdownServiceRunning} -> ${newStatus.isCountdownServiceRunning}")
        }
    }

    /**
     * 记录权限状态变化
     */
    private fun logPermissionStatusChanges(oldStatus: SystemStatus, newStatus: SystemStatus) {
        if (oldStatus.hasOverlayPermission != newStatus.hasOverlayPermission) {
            XLog.i("悬浮窗权限状态变化: ${oldStatus.hasOverlayPermission} -> ${newStatus.hasOverlayPermission}")
        }
        
        if (oldStatus.isDeviceOwner != newStatus.isDeviceOwner) {
            XLog.i("设备所有者权限状态变化: ${oldStatus.isDeviceOwner} -> ${newStatus.isDeviceOwner}")
        }
    }

    /**
     * 获取当前系统状态快照
     */
    fun getCurrentStatus(): SystemStatus {
        return _systemStatus.value
    }

    /**
     * 手动刷新系统状态
     */
    fun refreshStatus() {
        monitorScope.launch {
            try {
                val newStatus = SystemStatus(
                    hasOverlayPermission = checkOverlayPermission(),
                    isDeviceOwner = checkDeviceOwnerStatus(),
                    isFloatingWindowServiceRunning = checkServiceRunning("FloatingWindowService"),
                    isCountdownServiceRunning = checkServiceRunning("CountdownService"),
                    lastUpdateTime = System.currentTimeMillis()
                )
                
                _systemStatus.value = newStatus
                XLog.d("手动刷新系统状态完成")
                
            } catch (e: Exception) {
                XLog.e("手动刷新系统状态失败", e)
            }
        }
    }
}
