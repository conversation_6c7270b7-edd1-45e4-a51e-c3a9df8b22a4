package com.hwb.timecontroller.viewModel

import android.app.Application
import android.content.pm.PackageManager
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.hwb.timecontroller.business.AppDataCleanupManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.model.CleanupProgressItem
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 应用数据清理ViewModel
 * 处理应用数据清理流程的业务逻辑和状态管理
 * <p>
 * Author:huangwubin
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/8: First created this class.
 */
class AppDataCleanupViewModel(application: Application) : AndroidViewModel(application) {

    // 清理状态
    private val _cleanupState = MutableLiveData<CleanupState>()
    val cleanupState: LiveData<CleanupState> = _cleanupState

    // 清理进度列表
    private val _progressItems = MutableLiveData<List<CleanupProgressItem>>()
    val progressItems: LiveData<List<CleanupProgressItem>> = _progressItems

    // 状态提示文本
    private val _statusText = MutableLiveData<String>()
    val statusText: LiveData<String> = _statusText

    // 清理完成回调
    private val _cleanupCompleted = MutableLiveData<Boolean>()
    val cleanupCompleted: LiveData<Boolean> = _cleanupCompleted

    // 整体清理进度 (0-100)
    private val _cleanupProgress = MutableLiveData<Int>()
    val cleanupProgress: LiveData<Int> = _cleanupProgress

    // 当前进度项列表
    private val currentProgressItems = mutableListOf<CleanupProgressItem>()

    /**
     * 清理状态枚举
     */
    enum class CleanupState {
        PREPARING,      // 准备中
        BUSINESS_CHECK, // 业务判断中
        CLEANING,       // 清理中
        COMPLETED,      // 清理完成
        ERROR           // 清理出错
    }

    init {
        _cleanupState.value = CleanupState.PREPARING
        _statusText.value = "正在准备清理应用数据..."
        _progressItems.value = emptyList()
        _cleanupProgress.value = 0
    }

    /**
     * 开始清理流程
     */
    fun startCleanupProcess() {
        XLog.d("开始应用数据清理流程")

        viewModelScope.launch {
            try {
                XLog.d("清理流程第0步：优先清理本地数据")
                //优先清理本地数据
                UserManager.logout()

                XLog.d("清理流程第1步：开始业务判断")
                // 第一阶段：业务判断
                performBusinessCheck()

                XLog.d("清理流程第2步：准备清理列表")
                // 第二阶段：准备清理列表
                prepareCleanupList()

                XLog.d("清理流程第3步：执行清理")
                // 第三阶段：执行清理
                performCleanup()

            } catch (e: Exception) {
                XLog.e("清理流程执行失败", e)
                _cleanupState.value = CleanupState.ERROR
                _statusText.value = "清理过程发生错误: ${e.message}"
            }
        }
    }

    /**
     * 执行业务判断（1秒延时模拟）
     */
    private suspend fun performBusinessCheck() {
        XLog.d("开始执行业务判断")
        _cleanupState.value = CleanupState.BUSINESS_CHECK
        _statusText.value = "正在执行业务判断..."

        // 模拟1秒业务判断延时
        delay(1000)

        XLog.d("业务判断完成")
    }

    /**
     * 准备清理列表
     */
    private fun prepareCleanupList() {
        XLog.d("准备清理应用列表")
        _statusText.value = "正在准备清理列表..."

        try {
            // 获取数据清理的时间范围
            val startTime = UserManager.getDataCleanupStartTime()
            val endTime = System.currentTimeMillis()
            val isUserLoggedIn = UserManager.isUserLoggedIn()

            XLog.d("数据清理准备 - startTime: $startTime, endTime: $endTime, 用户登录状态: $isUserLoggedIn")

            // 获取需要清理的应用列表（基于时间范围）
            val appsToCleanup = if (startTime > 0) {
                XLog.d("使用时间范围获取清理应用: $startTime - $endTime")
                AppDataCleanupManager.getCleanupTargetAppsByTimeRange(startTime, endTime)
            } else {
                XLog.d("数据清理开始时间无效(startTime=$startTime)，使用记录的应用列表")
                AppDataCleanupManager.getCleanupTargetApps()
            }

            val packageManager = getApplication<Application>().packageManager

            // 创建进度项列表
            currentProgressItems.clear()
            appsToCleanup.forEach { packageName ->
                val appName = try {
                    val appInfo = packageManager.getApplicationInfo(packageName, 0)
                    packageManager.getApplicationLabel(appInfo).toString()
                } catch (e: PackageManager.NameNotFoundException) {
                    packageName // 如果获取不到应用名，使用包名
                }

                val progressItem = CleanupProgressItem(
                    packageName = packageName,
                    appName = appName,
                    status = CleanupProgressItem.CleanupStatus.WAITING
                )
                currentProgressItems.add(progressItem)
            }

            // 更新UI
            _progressItems.value = currentProgressItems.toList()
            _statusText.value = "准备清理 ${currentProgressItems.size} 个应用的数据"

            XLog.d("清理列表准备完成，共 ${currentProgressItems.size} 个应用")

        } catch (e: Exception) {
            XLog.e("准备清理列表失败", e)
            throw e
        }
    }

    /**
     * 执行清理操作
     */
    private suspend fun performCleanup() {
        XLog.d("开始执行应用数据清理")
        _cleanupState.value = CleanupState.CLEANING
        _statusText.value = "正在清理应用数据..."

        try {
            // 获取数据清理的时间范围
            val startTime = UserManager.getDataCleanupStartTime()
            val endTime = System.currentTimeMillis()

            // 检查是否有清理目标
            val targetApps = if (startTime > 0) {
                AppDataCleanupManager.getCleanupTargetAppsByTimeRange(startTime, endTime)
            } else {
                AppDataCleanupManager.getCleanupTargetApps()
            }
            XLog.d("清理目标应用数量: ${targetApps.size}")

            if (targetApps.isEmpty()) {
                // 如果没有清理目标，显示2秒模拟进度
                XLog.d("没有清理目标，显示模拟进度")
                simulateCleanupProgress()

                // 直接完成
                _cleanupState.value = CleanupState.COMPLETED
                _statusText.value = "清理完成 - 无需清理的应用"
                _cleanupProgress.value = 100
                _cleanupCompleted.value = true
                return
            }

            // 调用AppDataCleanupManager执行清理（基于时间范围）
            if (startTime > 0) {
                XLog.d("使用时间范围清理方法")
                AppDataCleanupManager.cleanupAppDataByTimeRange(
                    startTime, endTime,
                onProgress = { packageName, current, total ->
                    // 更新当前清理应用的状态
                    updateProgressItem(packageName, CleanupProgressItem.CleanupStatus.CLEANING, 50)
                    _statusText.value = "正在清理应用数据... ($current/$total)"

                    // 更新整体进度
                    val overallProgress = ((current.toFloat() / total.toFloat()) * 100).toInt()
                    _cleanupProgress.value = overallProgress
                    XLog.d("清理进度更新: $current/$total ($overallProgress%)")
                },
                onComplete = { successCount, failureCount, failedApps ->
                    // 更新所有应用的最终状态
                    updateFinalStatus(failedApps)

                    // 清理完成
                    _cleanupState.value = CleanupState.COMPLETED
                    _statusText.value = "清理完成 - 成功: $successCount, 失败: $failureCount"
                    _cleanupProgress.value = 100

                    XLog.d("时间范围应用数据清理完成 - 成功: $successCount, 失败: $failureCount")

                    // 通知清理完成
                    _cleanupCompleted.value = true
                }
                )
            } else {
                XLog.d("使用传统记录清理方法")
                AppDataCleanupManager.cleanupAllAppData(
                    onProgress = { packageName, current, total ->
                        // 更新当前清理应用的状态
                        updateProgressItem(packageName, CleanupProgressItem.CleanupStatus.CLEANING, 50)
                        _statusText.value = "正在清理应用数据... ($current/$total)"

                        // 更新整体进度
                        val overallProgress = ((current.toFloat() / total.toFloat()) * 100).toInt()
                        _cleanupProgress.value = overallProgress
                        XLog.d("清理进度更新: $current/$total ($overallProgress%)")
                    },
                    onComplete = { successCount, failureCount, failedApps ->
                        // 更新所有应用的最终状态
                        updateFinalStatus(failedApps)

                        // 清理完成
                        _cleanupState.value = CleanupState.COMPLETED
                        _statusText.value = "清理完成 - 成功: $successCount, 失败: $failureCount"
                        _cleanupProgress.value = 100

                        XLog.d("传统应用数据清理完成 - 成功: $successCount, 失败: $failureCount")

                        // 通知清理完成
                        _cleanupCompleted.value = true
                    }
                )
            }

        } catch (e: Exception) {
            XLog.e("执行清理操作失败", e)
            throw e
        }
    }

    /**
     * 模拟清理进度显示（仅在空列表时使用）
     */
    private suspend fun simulateCleanupProgress() {
        XLog.d("开始模拟清理进度显示（2秒）")

        // 模拟进度从0到90%，每次增加10%，间隔200ms，总共2秒
        for (progress in 0..90 step 10) {
            _cleanupProgress.value = progress
            XLog.d("模拟清理进度: $progress%")
            delay(200)
        }

        XLog.d("模拟清理进度显示完成")
    }

    /**
     * 更新指定应用的进度状态
     */
    private fun updateProgressItem(packageName: String, status: CleanupProgressItem.CleanupStatus, progress: Int = 0) {
        val index = currentProgressItems.indexOfFirst { it.packageName == packageName }
        if (index != -1) {
            currentProgressItems[index] = currentProgressItems[index].copy(
                status = status,
                progress = progress
            )
            _progressItems.value = currentProgressItems.toList()
        }
    }

    /**
     * 更新所有应用的最终状态
     */
    private fun updateFinalStatus(failedApps: List<String>) {
        currentProgressItems.forEachIndexed { index, item ->
            val finalStatus = if (failedApps.contains(item.packageName)) {
                CleanupProgressItem.CleanupStatus.FAILED
            } else {
                CleanupProgressItem.CleanupStatus.SUCCESS
            }
            
            currentProgressItems[index] = item.copy(
                status = finalStatus,
                progress = 100
            )
        }
        _progressItems.value = currentProgressItems.toList()
    }
}
