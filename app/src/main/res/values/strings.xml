<resources>
    <string name="app_name">美聚云控制器</string>

    <!-- MainActivity strings -->
    <string name="app_title">时间控制器</string>
    <string name="home_app_title">美聚云本</string>
    <string name="enter_minutes">请输入倒计时分钟数：</string>
    <string name="minutes_hint">分钟</string>
    <string name="start_countdown">开始计时</string>
    <string name="start_countdown_test">模拟计时</string>
    <string name="status_ready">准备就绪</string>
    <string name="status_countdown_started">倒计时已开始：%d 分钟</string>
    <string name="status_device_owner_required">需要设备所有者权限</string>
    <string name="status_setup_failed">设置失败</string>
    <string name="checking_device_owner">检查设备所有者状态...</string>
    <string name="device_owner_active">设备所有者：已激活</string>
    <string name="device_owner_inactive">设备所有者：未激活</string>
    <string name="error_enter_minutes">请输入分钟数</string>
    <string name="error_invalid_minutes">请输入有效的分钟数</string>
    <string name="countdown_started">倒计时已开始</string>

    <!-- LockActivity strings -->
    <string name="device_locked">设备已锁定</string>
    <string name="device_locked_message">倒计时结束，设备已进入锁定模式\n只能访问网络设置</string>
    <string name="network_settings">网络设置</string>
    <string name="unlock_instruction">等待网络解锁指令...</string>

    <!-- Notification strings -->
    <string name="notification_channel_name">倒计时通知</string>
    <string name="notification_channel_description">显示倒计时进度和设备锁定状态</string>
    <string name="notification_countdown_text">剩余时间：%1$d:%2$02d</string>
    <string name="notification_device_locked">设备已锁定</string>

    <!-- Whitelist strings -->
    <string name="whitelist_manager">白名单管理</string>
    <string name="whitelist_description">选择允许在设备锁定时使用的应用。系统应用（如设置、电话等）默认在白名单中。</string>
    <string name="go_to_settings">去设置</string>
    <string name="later">稍后设置</string>

    <!-- 悬浮窗相关 -->
    <string name="home_button_desc">打开主页应用</string>
    <string name="main_activity_button_desc">打开设置</string>
    <string name="recharge_reminder">请及时充值</string>
    <string name="floating_window_permission_required">需要悬浮窗权限才能显示倒计时</string>
    <string name="floating_window_permission_denied">悬浮窗权限被拒绝</string>
    <string name="governance_warning">非管控状态 · 点击进入设置</string>
    <string name="confirm_home_title">确认操作</string>
    <string name="confirm_home_message">确定要返回主页吗？</string>

    <!-- 应用启动验证相关 -->
    <string name="verifying_app_launch">正在验证应用启动权限...</string>
    <string name="app_launch_blocked">应用启动被阻止</string>
    <string name="payment_required">需要付费解锁</string>
    <string name="daily_limit_reached">今日启动次数已达上限</string>
    <string name="verification_timeout">验证超时</string>
    <string name="cancel">取消</string>
    <string name="confirm_exit_title">确认退出</string>
    <string name="confirm_exit_message">确定要退出验证吗？</string>
    <string name="confirm">确定</string>

    <!-- 登录验证界面相关 -->
    <string name="login_required_title">需要登录验证</string>
    <string name="login_required_message">请扫描下方二维码完成登录验证后继续使用</string>
    <string name="login_status_waiting">微信扫码租借设备</string>
    <string name="login_status_checking">正在确认登录...</string>
    <string name="login_status_success">登录成功！正在启动应用...</string>
    <string name="login_status_failed">登录失败，请重试</string>
    <string name="login_status_expired">登录已过期，请刷新二维码</string>
    <string name="login_qr_load_failed">登录二维码加载失败\n点击刷新重试</string>
    <string name="refresh_login_qr">点击刷新查看余额</string>
    <string name="cancel_login">返回</string>

    <!-- 保留付款相关字符串以兼容现有代码 -->
    <string name="payment_required_title">需要付费使用</string>
    <string name="payment_required_message">请扫描下方小程序码完成付费后继续使用</string>
    <string name="payment_status_waiting">等待付款中...</string>
    <string name="payment_status_checking">正在确认付款...</string>
    <string name="payment_status_success">付款成功！正在启动应用...</string>
    <string name="payment_status_failed">付款失败，请重试</string>
    <string name="payment_status_expired">付款已过期，请刷新小程序码</string>
    <string name="qr_code_load_failed">小程序码加载失败\n点击刷新重试</string>
    <string name="refresh_qr_code">刷新</string>
    <string name="cancel_payment">取消</string>
    <string name="network_error">网络连接失败</string>
    <string name="websocket_connecting">正在连接服务器...</string>
    <string name="websocket_connected">已连接到服务器</string>
    <string name="websocket_disconnected">与服务器连接断开</string>

    <!-- 启动页相关 -->
    <string name="app_loading">正在加载...</string>
    <string name="app_initializing">初始化中...</string>

</resources>